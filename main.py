#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云图数据同步到飞书多维表 - 主程序
"""

import argparse
import sys
from datetime import datetime, timedelta
from pathlib import Path

from yuntu_feishu_sync import SyncManager
from yuntu_feishu_sync.utils import setup_logger

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="云图数据同步到飞书多维表工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                          # 同步昨天的数据
  python main.py --date 2025-06-08        # 同步指定日期的数据
  python main.py --start 2025-06-01 --end 2025-06-07  # 同步日期范围的数据
  python main.py --test                   # 测试所有连接
  python main.py --schema                 # 读取表格结构
        """
    )
    
    parser.add_argument(
        '--date', 
        type=str, 
        help='指定同步日期 (YYYY-MM-DD)，默认为昨天'
    )
    
    parser.add_argument(
        '--start', 
        type=str, 
        help='开始日期 (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--end', 
        type=str, 
        help='结束日期 (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--test', 
        action='store_true', 
        help='测试所有连接'
    )
    
    parser.add_argument(
        '--schema', 
        action='store_true', 
        help='读取并保存表格结构'
    )
    
    parser.add_argument(
        '--verbose', '-v', 
        action='store_true', 
        help='详细输出'
    )
    
    return parser.parse_args()

def validate_date(date_str: str) -> bool:
    """验证日期格式"""
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志级别
    log_level = "DEBUG" if args.verbose else "INFO"
    logger = setup_logger(level=log_level)
    
    print("🚀 云图数据同步到飞书多维表工具")
    print("=" * 50)
    
    try:
        # 初始化同步管理器
        sync_manager = SyncManager()
        
        # 测试连接
        if args.test:
            print("🧪 执行连接测试...")
            results = sync_manager.test_all_connections()
            
            print("\n📊 测试结果:")
            for service, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"  {service}: {status}")
            
            if all(results.values()):
                print("\n🎉 所有连接测试通过！")
                return 0
            else:
                print("\n⚠️  部分连接测试失败，请检查配置")
                return 1
        
        # 读取表格结构
        if args.schema:
            print("📋 读取表格结构...")
            schema = sync_manager.get_table_schema()
            
            if schema:
                fields = schema.get('fields', [])
                print(f"✅ 成功读取表格结构: {len(fields)} 个字段")
                
                # 保存到文件
                file_path = sync_manager.save_table_schema()
                print(f"💾 结构已保存到: {file_path}")
                
                # 显示字段信息
                print("\n📝 字段列表:")
                for i, field in enumerate(fields, 1):
                    print(f"  {i:2d}. {field['field_name']} (类型: {field['field_type']})")
                
                return 0
            else:
                print("❌ 读取表格结构失败")
                return 1
        
        # 数据同步
        start_date = None
        end_date = None
        
        if args.date:
            # 指定单个日期
            if not validate_date(args.date):
                print(f"❌ 日期格式错误: {args.date}，请使用 YYYY-MM-DD 格式")
                return 1
            start_date = end_date = args.date
            
        elif args.start and args.end:
            # 指定日期范围
            if not validate_date(args.start) or not validate_date(args.end):
                print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
                return 1
            start_date = args.start
            end_date = args.end
            
        elif args.start or args.end:
            print("❌ 请同时指定开始日期和结束日期")
            return 1
        
        else:
            # 默认同步昨天的数据
            yesterday = datetime.now() - timedelta(days=1)
            start_date = end_date = yesterday.strftime('%Y-%m-%d')
        
        print(f"📅 同步日期范围: {start_date} 到 {end_date}")
        print()
        
        # 执行同步
        print("🔄 开始数据同步...")
        result = sync_manager.sync_data(start_date, end_date)
        
        if result.get('success'):
            print("✅ 数据同步成功！")
            print(f"📊 同步统计:")
            print(f"   总记录数: {result.get('total_records', 0)}")
            print(f"   新增记录: {result.get('added', 0)}")
            print(f"   更新记录: {result.get('updated', 0)}")
            print(f"   失败记录: {result.get('failed', 0)}")
            
            if result.get('failed', 0) > 0:
                print("⚠️  部分记录同步失败，请检查日志")
                return 1
            
            return 0
        else:
            print(f"❌ 数据同步失败: {result.get('error', '未知错误')}")
            return 1
    
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"❌ 程序执行出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
