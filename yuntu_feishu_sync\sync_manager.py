#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步管理器 - 协调云图数据同步到飞书
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from .core import FeishuClient, YuntuClient, DataProcessor
from .config import settings
from .utils import setup_logger

class SyncManager:
    """
    同步管理器 - 协调整个同步流程
    """
    
    def __init__(self):
        """初始化同步管理器"""
        self.logger = setup_logger()
        self.feishu_client = FeishuClient()
        self.yuntu_client = YuntuClient()
        self.data_processor = DataProcessor()
    
    def sync_data(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        执行数据同步
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)，默认为昨天
            end_date: 结束日期 (YYYY-MM-DD)，默认为昨天
            
        Returns:
            同步结果统计
        """
        try:
            # 设置默认日期
            if not start_date or not end_date:
                yesterday = datetime.now() - timedelta(days=1)
                default_date = yesterday.strftime('%Y-%m-%d')
                start_date = start_date or default_date
                end_date = end_date or default_date
            
            self.logger.info(f"开始同步数据: {start_date} 到 {end_date}")
            
            # 步骤1: 获取云图数据
            self.logger.info("步骤1: 获取云图数据")
            yuntu_data = self.yuntu_client.get_trigger_insight_data(start_date, end_date)
            
            if not yuntu_data:
                self.logger.error("获取云图数据失败")
                return {"success": False, "error": "获取云图数据失败"}
            
            # 步骤2: 处理数据
            self.logger.info("步骤2: 处理和转换数据")
            records = self.data_processor.process_yuntu_data(yuntu_data, start_date)
            
            if not records:
                self.logger.warning("没有数据需要同步")
                return {"success": True, "message": "没有数据需要同步", "added": 0, "updated": 0}
            
            # 验证和去重
            records = self.data_processor.validate_records(records)
            records = self.data_processor.deduplicate_records(records)
            
            # 步骤3: 同步到飞书
            self.logger.info("步骤3: 同步数据到飞书多维表")
            sync_result = self._sync_to_feishu(records)
            
            # 返回结果
            result = {
                "success": True,
                "start_date": start_date,
                "end_date": end_date,
                "total_records": len(records),
                **sync_result
            }
            
            self.logger.info(f"同步完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"同步过程中出错: {e}")
            return {"success": False, "error": str(e)}
    
    def _sync_to_feishu(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        同步数据到飞书
        
        Args:
            records: 要同步的记录
            
        Returns:
            同步结果
        """
        table_id = settings.feishu.table_id
        
        # 获取现有记录进行增量更新
        existing_records = self.feishu_client.get_table_records(table_id)
        
        # 分类记录：新增 vs 更新
        records_to_add = []
        records_to_update = []
        
        for record in records:
            # 使用触点ID和数据日期作为唯一键
            trigger_id = record.get('触点ID', '')
            data_date = record.get('数据日期', '')
            
            # 查找是否存在相同的记录
            found_existing = False
            for existing_id, existing_fields in existing_records.items():
                if (existing_fields.get('触点ID') == trigger_id and 
                    existing_fields.get('数据日期') == data_date):
                    # 找到现有记录，准备更新
                    records_to_update.append((existing_id, record))
                    found_existing = True
                    break
            
            if not found_existing:
                records_to_add.append(record)
        
        self.logger.info(f"分类完成: {len(records_to_add)} 条新增, {len(records_to_update)} 条更新")
        
        result = {"added": 0, "updated": 0, "failed": 0}
        
        # 批量添加新记录
        if records_to_add:
            success, added_count, failed_records = self.feishu_client.batch_add_records(
                table_id, records_to_add
            )
            result["added"] = added_count
            result["failed"] += len(failed_records)
        
        # 更新现有记录
        if records_to_update:
            updated_count = 0
            for record_id, new_fields in records_to_update:
                if self._update_single_record(table_id, record_id, new_fields):
                    updated_count += 1
                else:
                    result["failed"] += 1
            result["updated"] = updated_count
        
        return result
    
    def _update_single_record(self, table_id: str, record_id: str, fields: Dict[str, Any]) -> bool:
        """
        更新单条记录
        
        Args:
            table_id: 表格ID
            record_id: 记录ID
            fields: 字段数据
            
        Returns:
            是否成功
        """
        endpoint = f"bitable/v1/apps/{settings.feishu.base_id}/tables/{table_id}/records/{record_id}"
        data = {'fields': fields}
        
        result = self.feishu_client._make_api_request('PUT', endpoint, data)
        if result:
            self.logger.debug(f"成功更新记录 {record_id}")
            return True
        else:
            self.logger.error(f"更新记录 {record_id} 失败")
            return False
    
    def test_all_connections(self) -> Dict[str, bool]:
        """
        测试所有连接
        
        Returns:
            连接测试结果
        """
        self.logger.info("开始测试所有连接...")
        
        results = {}
        
        # 测试CookieCloud连接
        try:
            results["cookiecloud"] = self.yuntu_client.cookie_manager.test_connection()
        except Exception as e:
            self.logger.error(f"CookieCloud连接测试失败: {e}")
            results["cookiecloud"] = False
        
        # 测试云图API连接
        try:
            results["yuntu_api"] = self.yuntu_client.test_connection()
        except Exception as e:
            self.logger.error(f"云图API连接测试失败: {e}")
            results["yuntu_api"] = False
        
        # 测试飞书API连接
        try:
            token = self.feishu_client.get_access_token()
            results["feishu_api"] = token is not None
        except Exception as e:
            self.logger.error(f"飞书API连接测试失败: {e}")
            results["feishu_api"] = False
        
        self.logger.info(f"连接测试结果: {results}")
        return results
    
    def get_table_schema(self) -> Dict[str, Any]:
        """
        获取飞书表格结构
        
        Returns:
            表格结构信息
        """
        return self.feishu_client.get_table_schema(settings.feishu.table_id)
    
    def save_table_schema(self, file_path: Optional[str] = None) -> str:
        """
        保存表格结构到文件
        
        Args:
            file_path: 保存路径
            
        Returns:
            保存的文件路径
        """
        return self.feishu_client.save_schema_to_file(settings.feishu.table_id, file_path)
