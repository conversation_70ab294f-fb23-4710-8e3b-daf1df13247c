#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云图客户端 - 用于获取云图数据
"""

import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import urlencode

from ..config import settings, YUNTU_REQUEST_PARAMS
from ..utils import setup_logger
from .cookie_manager import CookieManager

class YuntuClient:
    """
    云图客户端 - 获取触点效果分析数据
    """
    
    def __init__(self):
        """初始化云图客户端"""
        self.logger = setup_logger()
        self.config = settings.yuntu
        self.cookie_manager = CookieManager()
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Origin': self.config.base_url,
            'Referer': f'{self.config.base_url}/yuntu_brand/ecom/evaluation_brand/history/distribution',
            'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br, zstd'
        })
    
    def _get_cookies(self) -> Dict[str, str]:
        """获取云图域名的cookie"""
        return self.cookie_manager.get_cookies_for_requests(self.config.domain)
    
    def _build_request_params(self, start_date: str, end_date: str, 
                             advertiser_id: Optional[str] = None) -> Dict[str, Any]:
        """
        构建请求参数
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            advertiser_id: 广告主ID
            
        Returns:
            请求参数字典
        """
        params = YUNTU_REQUEST_PARAMS.copy()
        params['start_date'] = start_date
        params['end_date'] = end_date
        
        # 如果提供了广告主ID，可以在这里设置
        # 目前使用配置中的默认值
        
        return params
    
    def get_trigger_insight_data(self, start_date: str, end_date: str,
                                advertiser_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        获取触点洞察效果分析数据
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            advertiser_id: 广告主ID
            
        Returns:
            API响应数据
        """
        # 构建URL
        url = f"{self.config.base_url}{self.config.api_endpoint}"
        
        # 添加查询参数
        if advertiser_id:
            url += f"?aadvid={advertiser_id}"
        else:
            # 使用默认的广告主ID
            url += "?aadvid=1731407470252045"
        
        # 构建请求体
        request_data = self._build_request_params(start_date, end_date, advertiser_id)
        
        # 获取cookie
        cookies = self._get_cookies()
        if not cookies:
            self.logger.error("未获取到有效的cookie")
            return None
        
        try:
            self.logger.info(f"请求云图数据: {start_date} 到 {end_date}")
            self.logger.debug(f"请求URL: {url}")
            self.logger.debug(f"请求参数: {json.dumps(request_data, ensure_ascii=False)}")
            
            response = self.session.post(
                url,
                json=request_data,
                cookies=cookies,
                timeout=self.config.timeout
            )
            
            self.logger.info(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('code') == 0:
                    self.logger.info("成功获取云图数据")
                    return result.get('data', {})
                else:
                    self.logger.error(f"API返回错误: {result}")
                    return None
            else:
                self.logger.error(f"HTTP错误 {response.status_code}: {response.text}")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return None
    
    def get_data_for_date_range(self, days: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取指定天数范围的数据
        
        Args:
            days: 天数，默认1天
            
        Returns:
            API响应数据
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days-1)
        
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        return self.get_trigger_insight_data(start_date_str, end_date_str)
    
    def get_yesterday_data(self) -> Optional[Dict[str, Any]]:
        """
        获取昨天的数据
        
        Returns:
            API响应数据
        """
        yesterday = datetime.now() - timedelta(days=1)
        date_str = yesterday.strftime('%Y-%m-%d')
        
        return self.get_trigger_insight_data(date_str, date_str)
    
    def test_connection(self) -> bool:
        """
        测试云图API连接
        
        Returns:
            连接是否成功
        """
        try:
            self.logger.info("测试云图API连接...")
            
            # 测试获取昨天的数据
            data = self.get_yesterday_data()
            
            if data:
                distribution_list = data.get('distribution_list', [])
                self.logger.info(f"连接成功! 获取到 {len(distribution_list)} 个触点数据")
                return True
            else:
                self.logger.error("连接失败: 未获取到数据")
                return False
                
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
